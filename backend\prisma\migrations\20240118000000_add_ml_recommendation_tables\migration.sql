-- CreateTable
CREATE TABLE "candidate_profiles" (
    "id" TEXT NOT NULL,
    "candidate_id" TEXT NOT NULL,
    "profile_version" TEXT NOT NULL DEFAULT '1.0',
    "skills_vector" TEXT,
    "experience_vector" TEXT,
    "education_score" DOUBLE PRECISION,
    "skills_score" DOUBLE PRECISION,
    "experience_score" DOUBLE PRECISION,
    "location_flexibility" DOUBLE PRECISION,
    "salary_flexibility" DOUBLE PRECISION,
    "culture_score" DOUBLE PRECISION,
    "performance_prediction" DOUBLE PRECISION,
    "retention_prediction" DOUBLE PRECISION,
    "personality_profile" TEXT,
    "work_style_preferences" TEXT,
    "motivation_factors" TEXT,
    "communication_style" TEXT,
    "leadership_potential" DOUBLE PRECISION,
    "teamwork_score" DOUBLE PRECISION,
    "adaptability_score" DOUBLE PRECISION,
    "market_value" DOUBLE PRECISION,
    "demand_score" DOUBLE PRECISION,
    "rarity_score" DOUBLE PRECISION,
    "competition_level" DOUBLE PRECISION,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "candidate_profiles_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "job_market_data" (
    "id" TEXT NOT NULL,
    "job_id" TEXT NOT NULL,
    "average_salary" DOUBLE PRECISION,
    "salary_percentile_25" DOUBLE PRECISION,
    "salary_percentile_75" DOUBLE PRECISION,
    "demand_score" DOUBLE PRECISION,
    "supply_score" DOUBLE PRECISION,
    "competition_level" DOUBLE PRECISION,
    "time_to_fill_average" INTEGER,
    "application_volume" INTEGER,
    "quality_score" DOUBLE PRECISION,
    "location_demand" DOUBLE PRECISION,
    "remote_compatibility" DOUBLE PRECISION,
    "relocation_likelihood" DOUBLE PRECISION,
    "industry_growth_rate" DOUBLE PRECISION,
    "role_evolution_score" DOUBLE PRECISION,
    "automation_risk" DOUBLE PRECISION,
    "skills_evolution_rate" DOUBLE PRECISION,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "job_market_data_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "candidate_job_matches" (
    "id" TEXT NOT NULL,
    "candidate_id" TEXT NOT NULL,
    "job_id" TEXT NOT NULL,
    "overall_match_score" DOUBLE PRECISION NOT NULL,
    "skills_match_score" DOUBLE PRECISION NOT NULL,
    "experience_match_score" DOUBLE PRECISION NOT NULL,
    "location_match_score" DOUBLE PRECISION NOT NULL,
    "salary_match_score" DOUBLE PRECISION NOT NULL,
    "culture_match_score" DOUBLE PRECISION NOT NULL,
    "matched_skills" TEXT,
    "missing_skills" TEXT,
    "overqualifications" TEXT,
    "risk_factors" TEXT,
    "strength_factors" TEXT,
    "success_probability" DOUBLE PRECISION,
    "retention_probability" DOUBLE PRECISION,
    "performance_score" DOUBLE PRECISION,
    "time_to_productivity" INTEGER,
    "model_version" TEXT NOT NULL,
    "confidence" DOUBLE PRECISION NOT NULL,
    "explanation" TEXT,
    "recommendation_rank" INTEGER,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "candidate_job_matches_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ml_interactions" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "candidate_id" TEXT NOT NULL,
    "job_id" TEXT NOT NULL,
    "action_type" TEXT NOT NULL,
    "interaction_context" TEXT,
    "candidate_score" DOUBLE PRECISION,
    "candidate_rank" INTEGER,
    "session_id" TEXT,
    "model_version" TEXT,
    "recommendation_id" TEXT,
    "ab_test_group" TEXT,
    "outcome" TEXT,
    "outcome_date" TIMESTAMP(3),
    "time_to_outcome" INTEGER,
    "satisfaction_score" INTEGER,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ml_interactions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ml_model_performance" (
    "id" TEXT NOT NULL,
    "model_id" TEXT NOT NULL,
    "evaluation_date" TIMESTAMP(3) NOT NULL,
    "accuracy" DOUBLE PRECISION NOT NULL,
    "precision" DOUBLE PRECISION NOT NULL,
    "recall" DOUBLE PRECISION NOT NULL,
    "f1_score" DOUBLE PRECISION NOT NULL,
    "auc" DOUBLE PRECISION NOT NULL,
    "calibration" DOUBLE PRECISION NOT NULL,
    "gender_bias" DOUBLE PRECISION,
    "age_bias" DOUBLE PRECISION,
    "location_bias" DOUBLE PRECISION,
    "education_bias" DOUBLE PRECISION,
    "experience_bias" DOUBLE PRECISION,
    "conversion_rate" DOUBLE PRECISION,
    "time_to_hire" DOUBLE PRECISION,
    "quality_of_hire" DOUBLE PRECISION,
    "retention_rate" DOUBLE PRECISION,
    "test_set_size" INTEGER NOT NULL,
    "training_set_size" INTEGER NOT NULL,
    "data_freshness" TIMESTAMP(3) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ml_model_performance_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "recommendation_batches" (
    "id" TEXT NOT NULL,
    "job_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "batch_size" INTEGER NOT NULL,
    "model_version" TEXT NOT NULL,
    "processing_time" INTEGER NOT NULL,
    "cache_hit" BOOLEAN NOT NULL,
    "request_filters" TEXT,
    "max_results" INTEGER,
    "min_score" DOUBLE PRECISION,
    "include_reasoning" BOOLEAN NOT NULL DEFAULT false,
    "average_score" DOUBLE PRECISION,
    "top_score" DOUBLE PRECISION,
    "score_distribution" TEXT,
    "diversity_score" DOUBLE PRECISION,
    "ab_test_group" TEXT,
    "experiment_id" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "recommendation_batches_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "feature_importance" (
    "id" TEXT NOT NULL,
    "model_id" TEXT NOT NULL,
    "feature_name" TEXT NOT NULL,
    "importance" DOUBLE PRECISION NOT NULL,
    "rank" INTEGER NOT NULL,
    "category" TEXT NOT NULL,
    "description" TEXT,
    "importance_std_dev" DOUBLE PRECISION,
    "stability_score" DOUBLE PRECISION,
    "business_impact" TEXT,
    "actionability" TEXT,
    "evaluation_date" TIMESTAMP(3) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "feature_importance_pkey" PRIMARY KEY ("id")
);

-- Add new columns to existing tables
ALTER TABLE "jobs" ADD COLUMN "job_portal" TEXT;
ALTER TABLE "jobs" ADD COLUMN "contact_person" TEXT;
ALTER TABLE "jobs" ADD COLUMN "contact_info" TEXT;
ALTER TABLE "jobs" ADD COLUMN "role" TEXT;
ALTER TABLE "jobs" ADD COLUMN "work_type" TEXT;
ALTER TABLE "jobs" ADD COLUMN "company_size_category" TEXT;
ALTER TABLE "jobs" ADD COLUMN "salary_min" INTEGER;
ALTER TABLE "jobs" ADD COLUMN "salary_max" INTEGER;
ALTER TABLE "jobs" ADD COLUMN "salary_currency" TEXT;
ALTER TABLE "jobs" ADD COLUMN "location_city" TEXT;
ALTER TABLE "jobs" ADD COLUMN "location_state" TEXT;
ALTER TABLE "jobs" ADD COLUMN "location_country" TEXT;
ALTER TABLE "jobs" ADD COLUMN "location_latitude" DOUBLE PRECISION;
ALTER TABLE "jobs" ADD COLUMN "location_longitude" DOUBLE PRECISION;
ALTER TABLE "jobs" ADD COLUMN "remote_work_allowed" BOOLEAN;
ALTER TABLE "jobs" ADD COLUMN "preference" TEXT;
ALTER TABLE "jobs" ADD COLUMN "required_skills_array" TEXT;
ALTER TABLE "jobs" ADD COLUMN "preferred_skills_array" TEXT;
ALTER TABLE "jobs" ADD COLUMN "industry_tags" TEXT;

ALTER TABLE "candidates" ADD COLUMN "experience_years" INTEGER;
ALTER TABLE "candidates" ADD COLUMN "current_position" TEXT;
ALTER TABLE "candidates" ADD COLUMN "current_company" TEXT;
ALTER TABLE "candidates" ADD COLUMN "education_level" TEXT;
ALTER TABLE "candidates" ADD COLUMN "education_field" TEXT;
ALTER TABLE "candidates" ADD COLUMN "skills_array" TEXT;
ALTER TABLE "candidates" ADD COLUMN "certifications" TEXT;
ALTER TABLE "candidates" ADD COLUMN "languages_spoken" TEXT;
ALTER TABLE "candidates" ADD COLUMN "availability_status" TEXT;
ALTER TABLE "candidates" ADD COLUMN "notice_period_days" INTEGER;
ALTER TABLE "candidates" ADD COLUMN "expected_salary_min" INTEGER;
ALTER TABLE "candidates" ADD COLUMN "expected_salary_max" INTEGER;
ALTER TABLE "candidates" ADD COLUMN "salary_currency" TEXT;
ALTER TABLE "candidates" ADD COLUMN "preferred_work_type" TEXT;
ALTER TABLE "candidates" ADD COLUMN "remote_work_preference" TEXT;
ALTER TABLE "candidates" ADD COLUMN "location_city" TEXT;
ALTER TABLE "candidates" ADD COLUMN "location_state" TEXT;
ALTER TABLE "candidates" ADD COLUMN "location_country" TEXT;
ALTER TABLE "candidates" ADD COLUMN "location_latitude" DOUBLE PRECISION;
ALTER TABLE "candidates" ADD COLUMN "location_longitude" DOUBLE PRECISION;
ALTER TABLE "candidates" ADD COLUMN "preferred_company_size" TEXT;
ALTER TABLE "candidates" ADD COLUMN "industry_preferences" TEXT;
ALTER TABLE "candidates" ADD COLUMN "career_goals" TEXT;
ALTER TABLE "candidates" ADD COLUMN "personality_traits" TEXT;
ALTER TABLE "candidates" ADD COLUMN "work_values" TEXT;

-- CreateIndex
CREATE UNIQUE INDEX "candidate_job_unique" ON "candidate_job_matches"("candidate_id", "job_id");

-- CreateIndex
CREATE UNIQUE INDEX "model_feature_date_unique" ON "feature_importance"("model_id", "feature_name", "evaluation_date");

-- AddForeignKey
ALTER TABLE "candidate_profiles" ADD CONSTRAINT "candidate_profiles_candidate_id_fkey" FOREIGN KEY ("candidate_id") REFERENCES "candidates"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "job_market_data" ADD CONSTRAINT "job_market_data_job_id_fkey" FOREIGN KEY ("job_id") REFERENCES "jobs"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "candidate_job_matches" ADD CONSTRAINT "candidate_job_matches_candidate_id_fkey" FOREIGN KEY ("candidate_id") REFERENCES "candidates"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "candidate_job_matches" ADD CONSTRAINT "candidate_job_matches_job_id_fkey" FOREIGN KEY ("job_id") REFERENCES "jobs"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ml_interactions" ADD CONSTRAINT "ml_interactions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ml_model_performance" ADD CONSTRAINT "ml_model_performance_model_id_fkey" FOREIGN KEY ("model_id") REFERENCES "ml_models"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "recommendation_batches" ADD CONSTRAINT "recommendation_batches_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "feature_importance" ADD CONSTRAINT "feature_importance_model_id_fkey" FOREIGN KEY ("model_id") REFERENCES "ml_models"("id") ON DELETE CASCADE ON UPDATE CASCADE;
