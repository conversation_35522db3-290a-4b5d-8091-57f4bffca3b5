{"name": "talentsol-ats-backend", "version": "1.0.0", "description": "Backend API for TalentSol ATS", "main": "dist/index.js", "type": "module", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:reset": "prisma migrate reset", "db:seed": "tsx src/seed.ts", "db:enhance": "tsx src/scripts/enhanceSyntheticData.ts", "db:seed-enhanced": "npm run db:seed && npm run db:enhance", "db:check": "tsx src/scripts/checkDatabase.ts", "setup-unified-data": "tsx src/scripts/setupUnifiedData.ts", "generate-synthetic": "tsx src/scripts/syntheticDataGenerator.ts", "generate-batch": "tsx src/scripts/batchDataGeneration.ts", "generate-minimal": "tsx src/scripts/generateMinimalData.ts", "import-csv": "tsx src/scripts/importFromCSV.ts", "validate-data": "tsx src/scripts/validateSyntheticData.ts", "data-minimal": "npm run db:check && npm run generate-minimal && npm run validate-data", "data-full": "npm run db:check && npm run generate-batch && npm run validate-data", "db:studio": "prisma studio", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest", "test:controllers": "vitest run src/__tests__/controllers", "test:performance": "vitest run src/__tests__/performance", "test:integration": "vitest run src/__tests__/integration"}, "dependencies": {"@fullcalendar/core": "^6.1.17", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/react": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@prisma/client": "^5.7.1", "@tanstack/react-query-devtools": "^5.80.6", "@types/node-cron": "^3.0.11", "@types/node-fetch": "^2.6.12", "@types/nodemailer": "^6.4.17", "@types/react-beautiful-dnd": "^13.1.8", "@types/socket.io-client": "^1.4.36", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "helmet": "^7.2.0", "immer": "^10.1.1", "ioredis": "^5.3.2", "js-yaml": "^4.1.0", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cache": "^5.1.2", "node-cron": "^4.1.0", "node-fetch": "^3.3.2", "nodemailer": "^7.0.3", "react-beautiful-dnd": "^13.1.1", "react-big-calendar": "^1.19.2", "redis": "^4.6.12", "response-time": "^2.3.2", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "tailwind-scrollbar": "^4.0.2", "zod": "^3.23.8"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/js-yaml": "^4.0.9", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.10.5", "@types/response-time": "^2.3.8", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "@vitest/ui": "^3.2.2", "eslint": "^8.56.0", "jsdom": "^26.1.0", "prisma": "^5.7.1", "supertest": "^7.1.1", "tsx": "^4.6.2", "typescript": "^5.3.3", "vitest": "^3.2.2"}, "keywords": ["ats", "recruitment", "api", "express", "prisma", "typescript"], "author": "<PERSON> (<PERSON><PERSON><PERSON>) <PERSON>", "license": "MIT"}