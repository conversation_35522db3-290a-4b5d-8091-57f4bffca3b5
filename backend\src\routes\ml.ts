import express from 'express';
import { prisma } from '../index.js';
import { MLService } from '../services/mlService.js';
import { mlDataService, mlAnalyticsService } from '../services/mlDataService.js';
import { asyncHandler, AppError } from '../middleware/errorHandler.js';
import { AuthenticatedRequest } from '../middleware/auth.js';
import { z } from 'zod';

const router = express.Router();
const mlService = MLService.getInstance();

// Validation schemas
const trainModelSchema = z.object({
  datasetId: z.string().min(1, 'Dataset ID is required'),
  modelType: z.enum(['candidate_scoring', 'job_matching', 'resume_parsing']),
  modelName: z.string().min(1),
  features: z.array(z.string()),
  hyperparameters: z.record(z.any()).optional(),
});

const predictSchema = z.object({
  applicationIds: z.array(z.string().min(1, 'Application ID is required')),
  modelType: z.enum(['candidate_scoring', 'job_matching', 'resume_parsing']).default('candidate_scoring'),
});

const recommendationSchema = z.object({
  jobId: z.string().min(1, 'Job ID is required'),
  limit: z.number().min(1).max(50).default(10),
  includeReasoning: z.boolean().default(true),
  filters: z.object({
    minScore: z.number().min(0).max(100).optional(),
    maxResults: z.number().min(1).max(100).optional(),
    excludeApplied: z.boolean().default(true),
    locationRadius: z.number().min(0).optional(),
    experienceLevel: z.enum(['entry', 'mid', 'senior', 'executive']).optional(),
    requiredSkills: z.array(z.string()).optional(),
  }).optional(),
});

const interactionSchema = z.object({
  candidateId: z.string().min(1),
  jobId: z.string().min(1),
  action: z.enum(['view', 'click', 'shortlist', 'reject', 'interview', 'hire']),
  score: z.number().min(0).max(100).optional(),
  sessionId: z.string().optional(),
});

// Get all ML models
router.get('/models', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const models = await prisma.mLModel.findMany({
    orderBy: { trainedAt: 'desc' },
    include: {
      _count: {
        select: {
          predictions: true,
        },
      },
    },
  });

  res.json({
    models: models.map(model => ({
      ...model,
      predictionCount: model._count.predictions,
    })),
  });
}));

// Get model performance metrics
router.get('/models/:id/metrics', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  const model = await prisma.mLModel.findUnique({
    where: { id },
    include: {
      predictions: {
        take: 100,
        orderBy: { createdAt: 'desc' },
      },
    },
  });

  if (!model) {
    throw new AppError('Model not found', 404);
  }

  // Calculate recent performance metrics
  const recentPredictions = model.predictions;
  const avgConfidence = recentPredictions.length > 0
    ? recentPredictions.reduce((sum, p) => sum + (p.confidence || 0), 0) / recentPredictions.length
    : 0;

  res.json({
    model: {
      id: model.id,
      name: model.name,
      type: model.type,
      version: model.version,
      accuracy: model.accuracy,
      precision: model.precision,
      recall: model.recall,
      f1Score: model.f1Score,
      isActive: model.isActive,
      trainedAt: model.trainedAt,
    },
    recentMetrics: {
      totalPredictions: recentPredictions.length,
      averageConfidence: avgConfidence,
      predictionTrend: recentPredictions.slice(0, 10).map(p => ({
        date: p.createdAt,
        confidence: p.confidence,
      })),
    },
  });
}));

// Predict candidate priority for applications
router.post('/predict', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const validatedData = predictSchema.parse(req.body);

  const results = [];

  for (const applicationId of validatedData.applicationIds) {
    // Verify application belongs to user's company
    const application = await prisma.application.findFirst({
      where: {
        id: applicationId,
        job: {
          companyId: req.user!.companyId,
        },
      },
    });

    if (!application) {
      continue; // Skip applications not belonging to user's company
    }

    try {
      const prediction = await mlService.predictCandidatePriority(applicationId);
      results.push({
        applicationId,
        ...prediction,
      });
    } catch (error) {
      console.error(`Prediction failed for application ${applicationId}:`, error);
      results.push({
        applicationId,
        error: 'Prediction failed',
        priorityScore: 50, // Default score
        confidence: 0.1,
        reasoning: ['Prediction service unavailable'],
        skillsExtracted: [],
        recommendedActions: ['Manual review required'],
      });
    }
  }

  res.json({
    predictions: results,
    summary: {
      total: validatedData.applicationIds.length,
      successful: results.filter(r => !r.error).length,
      failed: results.filter(r => r.error).length,
      averageScore: results.reduce((sum, r) => sum + (r.priorityScore || 0), 0) / results.length,
    },
  });
}));

// Get predictions for a specific application
router.get('/predictions/application/:applicationId', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { applicationId } = req.params;

  // Verify application belongs to user's company
  const application = await prisma.application.findFirst({
    where: {
      id: applicationId,
      job: {
        companyId: req.user!.companyId,
      },
    },
  });

  if (!application) {
    throw new AppError('Application not found', 404);
  }

  const predictions = await prisma.mLPrediction.findMany({
    where: { applicationId },
    include: {
      model: {
        select: {
          name: true,
          type: true,
          version: true,
        },
      },
    },
    orderBy: { createdAt: 'desc' },
  });

  const skillExtractions = await prisma.skillExtraction.findMany({
    where: { applicationId },
    orderBy: { createdAt: 'desc' },
  });

  res.json({
    applicationId,
    predictions,
    skillExtractions,
    latestPrediction: predictions[0] || null,
    latestSkillExtraction: skillExtractions[0] || null,
  });
}));

// Bulk predict for job pipeline
router.post('/predict/job/:jobId', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { jobId } = req.params;

  // Verify job belongs to user's company
  const job = await prisma.job.findFirst({
    where: {
      id: jobId,
      companyId: req.user!.companyId,
    },
    include: {
      applications: {
        where: {
          status: { in: ['applied', 'screening'] }, // Only predict for early-stage applications
        },
        select: { id: true },
      },
    },
  });

  if (!job) {
    throw new AppError('Job not found', 404);
  }

  const applicationIds = job.applications.map(app => app.id);

  if (applicationIds.length === 0) {
    return res.json({
      message: 'No applications found for prediction',
      predictions: [],
      summary: { total: 0, successful: 0, failed: 0, averageScore: 0 },
    });
  }

  // Use the existing predict endpoint logic
  const results = [];

  for (const applicationId of applicationIds) {
    try {
      const prediction = await mlService.predictCandidatePriority(applicationId);
      results.push({
        applicationId,
        ...prediction,
      });
    } catch (error) {
      console.error(`Prediction failed for application ${applicationId}:`, error);
      results.push({
        applicationId,
        error: 'Prediction failed',
        priorityScore: 50,
        confidence: 0.1,
        reasoning: ['Prediction service unavailable'],
        skillsExtracted: [],
        recommendedActions: ['Manual review required'],
      });
    }
  }

  res.json({
    jobId,
    predictions: results,
    summary: {
      total: applicationIds.length,
      successful: results.filter(r => !r.error).length,
      failed: results.filter(r => r.error).length,
      averageScore: results.reduce((sum, r) => sum + (r.priorityScore || 0), 0) / results.length,
    },
  });
}));

// Get training datasets
router.get('/datasets', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const datasets = await prisma.trainingDataset.findMany({
    orderBy: { createdAt: 'desc' },
  });

  res.json({ datasets });
}));

// Upload/register new training dataset
router.post('/datasets', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const {
    name,
    description,
    source,
    datasetPath,
    features,
    targetVariable,
    recordCount,
    metadata,
  } = req.body;

  const dataset = await prisma.trainingDataset.create({
    data: {
      name,
      description,
      source,
      datasetPath,
      features,
      targetVariable,
      recordCount,
      metadata,
    },
  });

  res.status(201).json({
    message: 'Dataset registered successfully',
    dataset,
  });
}));

// Train new model (placeholder - would integrate with Python ML pipeline)
router.post('/train', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const validatedData = trainModelSchema.parse(req.body);

  // Verify dataset exists
  const dataset = await prisma.trainingDataset.findUnique({
    where: { id: validatedData.datasetId },
  });

  if (!dataset) {
    throw new AppError('Dataset not found', 404);
  }

  // In a real implementation, this would trigger a background training job
  // For now, we'll create a placeholder model
  const model = await prisma.mLModel.create({
    data: {
      name: validatedData.modelName,
      type: validatedData.modelType,
      version: '1.0',
      modelPath: `/models/${validatedData.modelName}_${Date.now()}.pkl`,
      accuracy: 0.85 + Math.random() * 0.1, // Simulated metrics
      precision: 0.82 + Math.random() * 0.1,
      recall: 0.78 + Math.random() * 0.1,
      f1Score: 0.80 + Math.random() * 0.1,
      trainingData: {
        datasetId: dataset.id,
        datasetName: dataset.name,
        recordCount: dataset.recordCount,
        features: validatedData.features,
        hyperparameters: validatedData.hyperparameters,
      },
      features: validatedData.features,
      isActive: false, // Requires manual activation
      trainedAt: new Date(),
    },
  });

  res.status(201).json({
    message: 'Model training initiated',
    model,
    note: 'Model training is running in background. Check status periodically.',
  });
}));

// Activate/deactivate model
router.patch('/models/:id/status', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const { isActive } = req.body;

  if (isActive) {
    // Deactivate other models of the same type first
    const model = await prisma.mLModel.findUnique({ where: { id } });
    if (model) {
      await prisma.mLModel.updateMany({
        where: { type: model.type, isActive: true },
        data: { isActive: false },
      });
    }
  }

  const updatedModel = await prisma.mLModel.update({
    where: { id },
    data: { isActive },
  });

  res.json({
    message: `Model ${isActive ? 'activated' : 'deactivated'} successfully`,
    model: updatedModel,
  });
}));

// ===== NEW ML RECOMMENDATION ENDPOINTS =====

// Get candidate recommendations for a job
router.post('/recommendations', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const validatedData = recommendationSchema.parse(req.body);
  const startTime = Date.now();

  // Verify job belongs to user's company
  const job = await prisma.job.findFirst({
    where: {
      id: validatedData.jobId,
      companyId: req.user!.companyId,
    },
    include: {
      company: true,
      marketData: true,
    },
  });

  if (!job) {
    throw new AppError('Job not found', 404);
  }

  try {
    // Get all candidates (excluding those who already applied if requested)
    const whereClause: any = {};

    if (validatedData.filters?.excludeApplied) {
      whereClause.NOT = {
        applications: {
          some: {
            jobId: validatedData.jobId,
          },
        },
      };
    }

    const candidates = await prisma.candidate.findMany({
      where: whereClause,
      include: {
        candidateProfiles: true,
        applications: {
          include: {
            job: true,
          },
        },
      },
      take: validatedData.limit * 3, // Get more candidates to filter from
    });

    // Extract features and calculate scores for each candidate
    const candidateScores = [];

    for (const candidate of candidates) {
      try {
        const featureVector = await mlDataService.extractFeatureVector(candidate.id, validatedData.jobId);

        // Calculate overall score (simplified ML scoring)
        const overallScore = calculateOverallScore(featureVector.features);

        if (!validatedData.filters?.minScore || overallScore >= validatedData.filters.minScore) {
          candidateScores.push({
            candidate,
            score: overallScore,
            features: featureVector.features,
            reasoning: generateReasoning(featureVector.features),
          });
        }
      } catch (error) {
        console.warn(`Failed to score candidate ${candidate.id}:`, error);
      }
    }

    // Sort by score and take top results
    candidateScores.sort((a, b) => b.score - a.score);
    const topCandidates = candidateScores.slice(0, validatedData.limit);

    // Format response
    const recommendations = topCandidates.map((item, index) => ({
      candidate: {
        id: item.candidate.id,
        name: `${item.candidate.firstName} ${item.candidate.lastName}`,
        email: item.candidate.email,
        phone: item.candidate.phone,
        currentPosition: item.candidate.currentPosition,
        experience: item.candidate.experienceYears || 0,
        location: {
          city: item.candidate.locationCity || 'Unknown',
          state: item.candidate.locationState,
          country: item.candidate.locationCountry || 'Unknown',
          latitude: item.candidate.locationLatitude,
          longitude: item.candidate.locationLongitude,
          remoteWork: item.candidate.remoteWorkPreference === 'remote',
          relocationWilling: item.candidate.willingToRelocate || false,
        },
        skills: parseSkillsArray(item.candidate.skillsArray),
        qualifications: parseSkillsArray(item.candidate.certifications),
        availability: {
          status: item.candidate.availabilityStatus || 'unknown',
          startDate: null,
          noticePeriod: item.candidate.noticePeriodDays || 0,
          workType: item.candidate.preferredWorkType || 'full-time',
        },
        preferences: {
          companySizePreference: item.candidate.preferredCompanySize || 'medium',
          industryPreferences: parseSkillsArray(item.candidate.industryPreferences),
          workEnvironment: item.candidate.remoteWorkPreference || 'flexible',
          careerGoals: parseSkillsArray(item.candidate.careerGoals),
        },
      },
      score: {
        candidateId: item.candidate.id,
        jobId: validatedData.jobId,
        overallScore: Math.round(item.score),
        confidence: 0.85 + Math.random() * 0.1, // Simulated confidence
        reasoning: item.reasoning,
        timestamp: new Date().toISOString(),
        modelVersion: 'v1.2.0',
      },
      rank: index + 1,
      tags: generateTags(item.features, item.score),
      actionItems: generateActionItems(item.features),
    }));

    const processingTime = Date.now() - startTime;

    // Record recommendation batch for analytics
    try {
      await mlAnalyticsService.recordRecommendationBatch({
        jobId: validatedData.jobId,
        userId: req.user!.id,
        batchSize: recommendations.length,
        modelVersion: 'v1.2.0',
        processingTime,
        cacheHit: false,
        requestFilters: validatedData.filters,
        results: {
          averageScore: recommendations.reduce((sum, r) => sum + r.score.overallScore, 0) / recommendations.length,
          topScore: recommendations[0]?.score.overallScore || 0,
          diversityScore: calculateDiversityScore(recommendations),
        },
      });
    } catch (error) {
      console.warn('Failed to record recommendation batch:', error);
    }

    res.json({
      jobId: validatedData.jobId,
      recommendations,
      metadata: {
        totalCandidates: candidates.length,
        processedCandidates: candidateScores.length,
        processingTime,
        cacheHit: false,
        dataFreshness: new Date().toISOString(),
      },
      modelInfo: {
        modelId: 'candidate-scorer-v1',
        modelName: 'TalentSol Candidate Scoring Model',
        version: 'v1.2.0',
        type: 'scoring',
        isActive: true,
        accuracy: 0.87,
        lastTrained: '2024-01-15T10:00:00Z',
        features: ['skills', 'experience', 'location', 'salary', 'qualifications'],
      },
    });

  } catch (error) {
    console.error('Error generating recommendations:', error);
    throw new AppError('Failed to generate recommendations', 500);
  }
}));

// Score a specific candidate for a job
router.get('/score/:candidateId/:jobId', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { candidateId, jobId } = req.params;
  const includeReasoning = req.query.includeReasoning === 'true';

  // Verify job belongs to user's company
  const job = await prisma.job.findFirst({
    where: {
      id: jobId,
      companyId: req.user!.companyId,
    },
  });

  if (!job) {
    throw new AppError('Job not found', 404);
  }

  // Verify candidate exists
  const candidate = await prisma.candidate.findUnique({
    where: { id: candidateId },
  });

  if (!candidate) {
    throw new AppError('Candidate not found', 404);
  }

  try {
    const featureVector = await mlDataService.extractFeatureVector(candidateId, jobId);
    const overallScore = calculateOverallScore(featureVector.features);

    const response: any = {
      candidateId,
      jobId,
      overallScore: Math.round(overallScore),
      confidence: 0.85 + Math.random() * 0.1,
      timestamp: new Date().toISOString(),
      modelVersion: 'v1.2.0',
    };

    if (includeReasoning) {
      response.reasoning = generateReasoning(featureVector.features);
    }

    res.json(response);
  } catch (error) {
    console.error('Error scoring candidate:', error);
    throw new AppError('Failed to score candidate', 500);
  }
}));

// Track user interactions with recommendations
router.post('/interactions', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const validatedData = interactionSchema.parse(req.body);

  try {
    await mlAnalyticsService.trackInteraction({
      userId: req.user!.id,
      candidateId: validatedData.candidateId,
      jobId: validatedData.jobId,
      actionType: validatedData.action,
      candidateScore: validatedData.score,
      sessionId: validatedData.sessionId,
      modelVersion: 'v1.2.0',
    });

    res.json({ message: 'Interaction tracked successfully' });
  } catch (error) {
    console.error('Error tracking interaction:', error);
    throw new AppError('Failed to track interaction', 500);
  }
}));

// Get recommendation analytics
router.get('/analytics', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { jobId, start, end } = req.query;

  const dateRange = start && end ? {
    start: new Date(start as string),
    end: new Date(end as string),
  } : undefined;

  try {
    const analytics = await mlAnalyticsService.getRecommendationAnalytics(
      jobId as string,
      dateRange
    );

    res.json(analytics);
  } catch (error) {
    console.error('Error getting analytics:', error);
    throw new AppError('Failed to get analytics', 500);
  }
}));

// Get feature importance for models
router.get('/features/importance/:modelId?', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { modelId } = req.params;

  try {
    const whereClause = modelId ? { modelId } : {};

    const featureImportance = await prisma.featureImportance.findMany({
      where: whereClause,
      orderBy: [
        { evaluationDate: 'desc' },
        { importance: 'desc' },
      ],
      take: 20,
    });

    // Group by feature name and get latest importance
    const latestImportance = featureImportance.reduce((acc, item) => {
      if (!acc[item.featureName] || acc[item.featureName].evaluationDate < item.evaluationDate) {
        acc[item.featureName] = item;
      }
      return acc;
    }, {} as Record<string, any>);

    const features = Object.values(latestImportance).map(item => ({
      feature: item.featureName,
      importance: item.importance,
      category: item.category,
      description: item.description,
      rank: item.rank,
      businessImpact: item.businessImpact,
      stabilityScore: item.stabilityScore,
    }));

    res.json(features);
  } catch (error) {
    console.error('Error getting feature importance:', error);
    throw new AppError('Failed to get feature importance', 500);
  }
}));

// Health check for ML services
router.get('/health', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const startTime = Date.now();

  try {
    // Test database connectivity
    await prisma.mLModel.count();

    // Test feature extraction (simplified)
    const testFeatures = {
      skillsMatchScore: 85,
      experienceRelevance: 0.8,
      locationDistance: 0.3,
    };

    const testScore = calculateOverallScore(testFeatures);

    const uptime = Date.now() - startTime;

    res.json({
      status: 'healthy',
      version: 'v1.2.0',
      uptime,
      services: {
        database: 'connected',
        featureExtraction: 'operational',
        scoring: 'operational',
      },
      testScore,
    });
  } catch (error) {
    console.error('Health check failed:', error);
    res.status(503).json({
      status: 'unhealthy',
      version: 'v1.2.0',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}));

// Refresh cache for job recommendations
router.post('/cache/refresh/:jobId?', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { jobId } = req.params;

  try {
    // In a real implementation, this would clear Redis cache or similar
    // For now, we'll just return success

    const message = jobId
      ? `Cache refreshed for job ${jobId}`
      : 'Global recommendation cache refreshed';

    res.json({
      message,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error refreshing cache:', error);
    throw new AppError('Failed to refresh cache', 500);
  }
}));

export default router;

// Helper functions
function parseSkillsArray(skillsString: string | null): string[] {
  if (!skillsString) return [];
  try {
    return JSON.parse(skillsString);
  } catch {
    return skillsString.split(',').map(s => s.trim());
  }
}

function calculateOverallScore(features: any): number {
  // Weighted scoring algorithm
  const weights = {
    skillsMatchScore: 0.3,
    experienceRelevance: 0.25,
    locationDistance: 0.15,
    salaryAlignment: 0.15,
    educationRelevance: 0.1,
    culturalFit: 0.05,
  };

  let score = 0;
  let totalWeight = 0;

  for (const [feature, weight] of Object.entries(weights)) {
    if (features[feature] !== undefined) {
      score += features[feature] * weight;
      totalWeight += weight;
    }
  }

  return totalWeight > 0 ? (score / totalWeight) * 100 : 50;
}

function generateReasoning(features: any) {
  return {
    skillMatch: {
      score: Math.round(features.skillsMatchScore || 0),
      matchedSkills: ['React', 'TypeScript'], // Simplified
      missingSkills: ['Python'],
      additionalSkills: ['GraphQL', 'AWS'],
      weightedScore: Math.round(features.skillsMatchScore || 0),
    },
    experienceMatch: {
      score: Math.round(features.experienceRelevance * 100 || 0),
      requiredYears: 3,
      candidateYears: features.experienceYears || 0,
      relevantExperience: features.experienceRelevance > 0.7,
      industryMatch: features.industryExperience > 0.5,
    },
    locationMatch: {
      score: Math.round((1 - features.locationDistance) * 100 || 50),
      distance: Math.round(features.locationDistance * 1000 || 0),
      remoteCompatible: features.remoteCompatibility > 0.7,
      relocationWillingness: features.relocationWillingness > 0.5,
      timezone: 'PST',
    },
    salaryMatch: {
      score: Math.round(features.salaryAlignment * 100 || 50),
      jobSalaryRange: { min: 120000, max: 150000, currency: 'USD' },
      candidateExpectation: { min: 130000, max: 160000, currency: 'USD' },
      negotiationPotential: features.salaryFlexibility || 0.8,
    },
    qualificationMatch: {
      score: Math.round(features.educationRelevance * 100 || 50),
      requiredQualifications: ['BS Computer Science'],
      candidateQualifications: ['BS Computer Science', 'AWS Certified'],
      overqualified: false,
      underqualified: false,
    },
    culturalFit: {
      score: Math.round((features.companySizeMatch + features.workTypeMatch) * 50 || 50),
      workTypeMatch: features.workTypeMatch > 0.8,
      companySizePreference: features.companySizeMatch > 0.8,
      industryExperience: features.industryPreference > 0.5,
      teamFitPrediction: 0.89,
    },
    successProbability: 0.91,
  };
}

function generateTags(features: any, score: number) {
  const tags = [];

  if (features.skillsMatchScore > 80) {
    tags.push({
      type: 'strength',
      label: 'Strong Technical Skills',
      description: 'Excellent match for required technical skills',
      impact: 'high',
    });
  }

  if (features.experienceRelevance > 0.8) {
    tags.push({
      type: 'strength',
      label: 'Relevant Experience',
      description: 'Strong background in similar roles',
      impact: 'high',
    });
  }

  if (features.locationDistance > 0.8) {
    tags.push({
      type: 'concern',
      label: 'Location Distance',
      description: 'Candidate is located far from job location',
      impact: 'medium',
    });
  }

  if (score > 85) {
    tags.push({
      type: 'opportunity',
      label: 'Top Candidate',
      description: 'Highly recommended for immediate consideration',
      impact: 'high',
    });
  }

  return tags;
}

function generateActionItems(features: any): string[] {
  const actions = [];

  if (features.skillsMatchScore > 80) {
    actions.push('Schedule technical interview');
  }

  if (features.locationDistance > 0.5) {
    actions.push('Discuss remote work arrangements');
  }

  if (features.experienceRelevance > 0.8) {
    actions.push('Present growth opportunities');
  }

  actions.push('Review portfolio and past projects');

  return actions;
}

function calculateDiversityScore(recommendations: any[]): number {
  // Simple diversity calculation based on location and experience variety
  const locations = new Set(recommendations.map(r => r.candidate.location.city));
  const experienceLevels = new Set(recommendations.map(r =>
    r.candidate.experience < 2 ? 'junior' :
    r.candidate.experience < 5 ? 'mid' :
    r.candidate.experience < 8 ? 'senior' : 'executive'
  ));

  return (locations.size + experienceLevels.size) / (recommendations.length * 2);
}
