/**
 * ML API Service
 * Handles communication with ML recommendation services
 */

import { 
  RecommendationRequest, 
  RecommendationResponse, 
  BatchRecommendationRequest,
  BatchRecommendationResponse,
  CandidateScore,
  MLModelConfig,
  RecommendationAnalytics,
  FeatureImportance,
  MLApiResponse
} from '@/types/ml-recommendations';

class MLApiService {
  private baseUrl: string;
  private apiKey?: string;

  constructor() {
    this.baseUrl = import.meta.env.VITE_ML_API_URL || 'http://localhost:3001/api/ml';
    this.apiKey = import.meta.env.VITE_ML_API_KEY;
  }

  private async request<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<MLApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (this.apiKey) {
      headers['Authorization'] = `Bearer ${this.apiKey}`;
    }

    // Add demo token for development
    const token = localStorage.getItem('auth-token') || 'demo-token-for-development';
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      if (!response.ok) {
        throw new Error(`ML API Error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('ML API request failed:', error);
      throw error;
    }
  }

  // Core recommendation methods
  async getCandidateRecommendations(
    request: RecommendationRequest
  ): Promise<RecommendationResponse> {
    const response = await this.request<RecommendationResponse>(
      '/recommendations',
      {
        method: 'POST',
        body: JSON.stringify(request),
      }
    );
    return response.data;
  }

  async getBatchRecommendations(
    request: BatchRecommendationRequest
  ): Promise<BatchRecommendationResponse> {
    const response = await this.request<BatchRecommendationResponse>(
      '/recommendations/batch',
      {
        method: 'POST',
        body: JSON.stringify(request),
      }
    );
    return response.data;
  }

  async scoreCandidateForJob(
    candidateId: string,
    jobId: string,
    includeReasoning = true
  ): Promise<CandidateScore> {
    const response = await this.request<CandidateScore>(
      `/score/${candidateId}/${jobId}?includeReasoning=${includeReasoning}`,
      { method: 'GET' }
    );
    return response.data;
  }

  async scoreCandidatesForJob(
    candidateIds: string[],
    jobId: string
  ): Promise<CandidateScore[]> {
    const response = await this.request<CandidateScore[]>(
      `/score/batch`,
      {
        method: 'POST',
        body: JSON.stringify({ candidateIds, jobId }),
      }
    );
    return response.data;
  }

  // Model management
  async getAvailableModels(): Promise<MLModelConfig[]> {
    const response = await this.request<MLModelConfig[]>('/models');
    return response.data;
  }

  async getModelInfo(modelId: string): Promise<MLModelConfig> {
    const response = await this.request<MLModelConfig>(`/models/${modelId}`);
    return response.data;
  }

  async updateModelConfig(
    modelId: string, 
    config: Partial<MLModelConfig>
  ): Promise<MLModelConfig> {
    const response = await this.request<MLModelConfig>(
      `/models/${modelId}`,
      {
        method: 'PATCH',
        body: JSON.stringify(config),
      }
    );
    return response.data;
  }

  // Analytics and performance
  async getRecommendationAnalytics(
    jobId?: string,
    dateRange?: { start: string; end: string }
  ): Promise<RecommendationAnalytics> {
    const params = new URLSearchParams();
    if (jobId) params.append('jobId', jobId);
    if (dateRange) {
      params.append('start', dateRange.start);
      params.append('end', dateRange.end);
    }

    const response = await this.request<RecommendationAnalytics>(
      `/analytics?${params.toString()}`
    );
    return response.data;
  }

  async getFeatureImportance(modelId?: string): Promise<FeatureImportance[]> {
    const endpoint = modelId ? `/features/importance/${modelId}` : '/features/importance';
    const response = await this.request<FeatureImportance[]>(endpoint);
    return response.data;
  }

  // User interaction tracking
  async trackUserInteraction(interaction: {
    action: string;
    candidateId: string;
    jobId: string;
    score?: number;
    sessionId?: string;
  }): Promise<void> {
    await this.request('/interactions', {
      method: 'POST',
      body: JSON.stringify({
        ...interaction,
        timestamp: new Date().toISOString(),
        userId: 'current-user', // TODO: Get from auth context
      }),
    });
  }

  async trackOutcome(outcome: {
    recommendationId: string;
    candidateId: string;
    jobId: string;
    outcome: string;
    metadata?: Record<string, any>;
  }): Promise<void> {
    await this.request('/outcomes', {
      method: 'POST',
      body: JSON.stringify({
        ...outcome,
        timestamp: new Date().toISOString(),
      }),
    });
  }

  // A/B Testing support
  async getABTestVariant(testId: string): Promise<string> {
    const response = await this.request<{ variant: string }>(`/ab-test/${testId}`);
    return response.data.variant;
  }

  async trackABTestConversion(
    testId: string,
    variant: string,
    conversionType: string
  ): Promise<void> {
    await this.request('/ab-test/conversion', {
      method: 'POST',
      body: JSON.stringify({
        testId,
        variant,
        conversionType,
        timestamp: new Date().toISOString(),
      }),
    });
  }

  // Utility methods
  async healthCheck(): Promise<{ status: string; version: string; uptime: number }> {
    const response = await this.request<{ status: string; version: string; uptime: number }>('/health');
    return response.data;
  }

  async refreshCache(jobId?: string): Promise<void> {
    const endpoint = jobId ? `/cache/refresh/${jobId}` : '/cache/refresh';
    await this.request(endpoint, { method: 'POST' });
  }

  // Mock data for development
  async getMockRecommendations(jobId: string): Promise<RecommendationResponse> {
    // Return mock data when ML service is not available
    return {
      jobId,
      recommendations: [
        {
          candidate: {
            id: 'cand_1',
            name: 'Sarah Chen',
            email: '<EMAIL>',
            avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
            currentPosition: 'Senior Frontend Developer',
            experience: 5,
            location: {
              city: 'San Francisco',
              state: 'CA',
              country: 'USA',
              remoteWork: true,
              relocationWilling: false,
            },
            skills: ['React', 'TypeScript', 'Node.js', 'GraphQL'],
            qualifications: ['BS Computer Science', 'AWS Certified'],
            availability: {
              status: 'employed',
              startDate: '2024-02-01',
              noticePeriod: 14,
              workType: 'full-time',
            },
            preferences: {
              companySizePreference: 'medium',
              industryPreferences: ['Technology', 'FinTech'],
              workEnvironment: 'hybrid',
              careerGoals: ['Technical Leadership', 'Product Development'],
            },
          },
          score: {
            candidateId: 'cand_1',
            jobId,
            overallScore: 92,
            confidence: 0.87,
            reasoning: {
              skillMatch: {
                score: 95,
                matchedSkills: ['React', 'TypeScript'],
                missingSkills: ['Python'],
                additionalSkills: ['GraphQL', 'AWS'],
                weightedScore: 95,
              },
              experienceMatch: {
                score: 90,
                requiredYears: 3,
                candidateYears: 5,
                relevantExperience: true,
                industryMatch: true,
              },
              locationMatch: {
                score: 85,
                distance: 0,
                remoteCompatible: true,
                relocationWillingness: false,
                timezone: 'PST',
              },
              salaryMatch: {
                score: 88,
                jobSalaryRange: { min: 120000, max: 150000, currency: 'USD' },
                candidateExpectation: { min: 130000, max: 160000, currency: 'USD' },
                negotiationPotential: 0.8,
              },
              qualificationMatch: {
                score: 92,
                requiredQualifications: ['BS Computer Science'],
                candidateQualifications: ['BS Computer Science', 'AWS Certified'],
                overqualified: false,
                underqualified: false,
              },
              culturalFit: {
                score: 89,
                workTypeMatch: true,
                companySizePreference: true,
                industryExperience: true,
                teamFitPrediction: 0.89,
              },
              successProbability: 0.91,
            },
            timestamp: new Date().toISOString(),
            modelVersion: 'v1.2.0',
          },
          rank: 1,
          tags: [
            {
              type: 'strength',
              label: 'Strong Technical Skills',
              description: 'Excellent match for required technical skills',
              impact: 'high',
            },
            {
              type: 'opportunity',
              label: 'Career Growth Potential',
              description: 'Looking for leadership opportunities',
              impact: 'medium',
            },
          ],
          actionItems: [
            'Schedule technical interview',
            'Discuss remote work arrangements',
            'Present growth opportunities',
          ],
        },
      ],
      metadata: {
        totalCandidates: 150,
        processedCandidates: 150,
        processingTime: 245,
        cacheHit: false,
        dataFreshness: new Date().toISOString(),
        abTestGroup: 'control',
      },
      modelInfo: {
        modelId: 'candidate-scorer-v1',
        modelName: 'TalentSol Candidate Scoring Model',
        version: 'v1.2.0',
        type: 'scoring',
        isActive: true,
        accuracy: 0.87,
        lastTrained: '2024-01-15T10:00:00Z',
        features: ['skills', 'experience', 'location', 'salary', 'qualifications'],
      },
    };
  }
}

export const mlApi = new MLApiService();
export default mlApi;
